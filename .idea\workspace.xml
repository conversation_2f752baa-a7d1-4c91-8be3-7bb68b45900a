<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5d038f75-6280-47d5-ac96-4a74e9cd67e4" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/docs/SERVER.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/SERVER.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/config/unified_scheduler_config.json" beforeDir="false" afterPath="$PROJECT_DIR$/server/config/unified_scheduler_config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/gate/live_info_till_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/gate/live_info_till_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/unified_data_scheduler.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/unified_data_scheduler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/unified_scheduler_main.py" beforeDir="false" afterPath="$PROJECT_DIR$/server/unified_scheduler_main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/live_sql.py" beforeDir="false" afterPath="$PROJECT_DIR$/sql/live_sql.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="31GrZ3mYjYvAKqRZrr9550sxjcR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\gitrepo\vupbi\server\base" />
      <recent name="E:\gitrepo\vupbi" />
      <recent name="E:\gitrepo\vupbi\vupbi" />
      <recent name="E:\gitrepo\vupbi\server" />
      <recent name="E:\gitrepo\vupbi\sql" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.17890.14" />
        <option value="bundled-python-sdk-5b207ade9991-7e9c3bbb6e34-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.17890.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5d038f75-6280-47d5-ac96-4a74e9cd67e4" name="更改" comment="" />
      <created>1755161303877</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755161303877</updated>
      <workItem from="1755161305009" duration="27977000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>